{"name": "coos-dms-ui-h5", "version": "1.0.0", "description": "纪检监察移动端H5应用", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint --ext .js,.vue packages", "lint:fix": "eslint --fix --ext .js,.vue packages", "commit": "git add . && git-cz"}, "dependencies": {"@vant/touch-emulator": "^1.4.0", "axios": "^1.6.0", "echarts": "^5.4.3", "pinia": "^2.1.7", "vant": "^4.8.0", "vue": "^3.4.0", "vue-echarts": "^6.6.1", "vue-router": "^4.2.5"}, "devDependencies": {"@commitlint/cli": "^19.7.1", "@commitlint/config-conventional": "^19.7.1", "@vitejs/plugin-vue": "^4.5.0", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.2", "git-cz": "4.8.0", "postcss": "^8.1.0", "postcss-pxtorem": "^6.1.0", "prettier": "^3.4.1", "sass": "^1.69.5", "vite": "^5.0.0"}, "lint-staged": {"*.{js,jsx,vue}": ["prettier --write src", "pnpm lint:fix"], "*.{html,sass,scss,less}": ["prettier --write src"], "*.md": ["prettier --write src"]}}