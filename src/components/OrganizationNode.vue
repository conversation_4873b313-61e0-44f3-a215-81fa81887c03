<template>
  <div class="organization-item" :style="{ marginLeft: `${level * 10}px` }">
    <!-- Organization Header -->
    <div class="org-header" @click="$emit('toggle', organization.id, level)">
      <div class="org-info">
        <div
          class="org-icon"
          :style="{ background: `linear-gradient(135deg, ${getOrgColor(level)} 0%, ${getOrgColor(level)}aa 100%)` }"
        >
          <van-icon name="cluster-o" />
        </div>
        <div class="org-info-content">
          <div class="org-details">
            <div class="org-name">{{ organization.name }}</div>
            <div class="org-count">{{ getTotalMemberCount(organization) }}人</div>
          </div>
          <van-icon :name="organization.expanded ? 'arrow-down' : 'arrow'" class="expand-icon" />
        </div>
      </div>
    </div>

    <!-- Children (when expanded) -->
    <div v-if="organization.expanded" class="org-children">
      <!-- Child Organizations -->
      <OrganizationNode
        v-for="childOrg in organization.children"
        :key="childOrg.id"
        :organization="childOrg"
        :level="level + 1"
        @toggle="(id, level) => $emit('toggle', id, level)"
        @staff-click="(staff) => $emit('staff-click', staff)"
      />

      <!-- Staff Members -->
      <div v-if="organization.members && organization.members.length > 0" class="staff-list">
        <div
          v-for="staff in organization.members"
          :key="staff.id"
          class="staff-item"
          @click="$emit('staff-click', staff)"
        >
          <div class="staff-avatar">
            <van-icon :name="staff.gender === '男' ? 'manager' : 'contact'" />
          </div>
          <div class="staff-info">
            <div class="staff-name">{{ staff.name }}</div>
            <div class="staff-details">{{ staff.position }} | {{ staff.gender }} | {{ staff.age }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  organization: {
    type: Object,
    required: true,
  },
  level: {
    type: Number,
    default: 0,
  },
});

defineEmits(['toggle', 'staff-click']);

const levelColors = [
  '#667eea', // 蓝紫色
  '#52c41a', // 绿色
  '#fa8c16', // 橙色
  '#eb2f96', // 粉色
];

const getOrgColor = (level) => levelColors[level] || levelColors[levelColors.length - 1];

const getTotalMemberCount = (org) => {
  let count = org.members ? org.members.length : 0;
  if (org.children) {
    count += org.children.reduce((sum, child) => sum + getTotalMemberCount(child), 0);
  }
  return count;
};
</script>

<style lang="scss" scoped>
.organization-item {
  &:not(:last-child) {
    margin-bottom: 8px;
  }

  .org-header {
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;

    .org-info {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex: 1;
      &-content {
        padding: 4px 0;
        flex-grow: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid var(--van-border-color);
      }

      .expand-icon {
        font-size: 14px;
        color: var(--van-text-color-2);
        margin-right: 8px;
        transition: transform 0.2s ease;
      }

      .org-icon {
        width: 32px;
        height: 32px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10px;

        .van-icon {
          color: #fff;
          font-size: 16px;
        }
      }

      .org-details {
        .org-name {
          height: 22px;
          font-weight: 500;
          font-size: 15px;
          color: var(--van-text-color);
          line-height: 22px;
        }

        .org-count {
          font-size: 11px;
          color: var(--van-text-color-2);
        }
      }
    }
  }

  .org-children {
    margin-top: 6px;
    margin-left: 12px;
    position: relative;

    .staff-list {
      margin-top: 6px;

      .staff-item {
        background: #f8f9fa;
        border-radius: 4px;
        padding: 8px 10px;
        margin-bottom: 4px;
        display: flex;
        align-items: center;
        cursor: pointer;
        transition: background 0.2s ease;
        margin-left: 10px;
        position: relative;

        &:hover {
          background: #f0f0f0;
        }

        .staff-avatar {
          width: 28px;
          height: 28px;
          border-radius: 50%;
          background: linear-gradient(135deg, var(--van-success-color) 0%, #389e0d 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 8px;

          .van-icon {
            color: #fff;
            font-size: 14px;
          }
        }

        .staff-info {
          flex: 1;

          .staff-name {
            height: 22px;
            font-size: 15px;
            color: var(--van-text-color);
            line-height: 22px;
          }

          .staff-details {
            height: 20px;
            font-size: 12px;
            color: var(--van-text-color-2);
            line-height: 20px;
          }
        }
      }
    }
  }
}
</style>
