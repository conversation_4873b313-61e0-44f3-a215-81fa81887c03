<!--
  * @Author: <PERSON><PERSON> <EMAIL>
  * @Date: 2025-08-14 18:01:21
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-14 19:20:48
 * @FilePath: /coos-dms-ui-app/src/components/section.vue
  * @Description:
 -->
<template>
  <div class="section">
    <div class="section-header">
      <div class="title">{{ title }}</div>
      <div v-if="supportRefresh" class="refresh" @click="handleRefresh"><van-icon name="replay" />刷新</div>
    </div>
    <div class="section-content">
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    default: '',
  },
  supportRefresh: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(['refresh']);
const handleRefresh = () => {
  // 刷新逻辑
  emit('refresh');
};
</script>

<style lang="scss" scoped>
.section {
  background: #fff;
  margin: 16px 16px 0;
  border-radius: 12px;
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 12px 0;

    .title {
      font-weight: 500;
      font-size: 16px;
      color: #15224c;
      line-height: 24px;
    }
    .refresh {
      height: 20px;
      font-size: 14px;
      color: #9aa7bf;
      line-height: 20px;
    }
  }

  .section-content {
    padding: 10px 12px 16px;
  }
}
</style>
