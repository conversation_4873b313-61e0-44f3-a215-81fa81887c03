/**
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-08-14 16:26:53
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-14 17:33:18
 * @FilePath: /coos-dms-ui-app/src/router/index.js
 * @Description:
 */
import { createRouter, createWebHistory } from 'vue-router';

const routes = [];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - 纪检监察移动端`;
  }
  next();
});

export default router;
