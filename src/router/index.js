/**
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-08-14 16:26:53
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-14 17:33:18
 * @FilePath: /coos-dms-ui-app/src/router/index.js
 * @Description:
 */
import { createRouter, createWebHistory } from 'vue-router';

const routes = [
  {
    path: '/',
    redirect: '/home'
  },
  {
    path: '/',
    component: () => import('@/views/Layout.vue'),
    children: [
      {
        path: '/home',
        name: 'Home',
        component: () => import('@/views/Home.vue'),
        meta: { title: '首页' }
      },
      {
        path: '/staff',
        name: 'Staff',
        component: () => import('@/views/Staff.vue'),
        meta: { title: '纪检队伍' }
      },
      {
        path: '/training',
        name: 'Training',
        component: () => import('@/views/Training.vue'),
        meta: { title: '培训管理' }
      },
      {
        path: '/ai',
        name: 'AI',
        component: () => import('@/views/AI.vue'),
        meta: { title: 'AI智能检索' }
      }
    ]
  },
  {
    path: '/staff/:id',
    name: 'StaffDetail',
    component: () => import('@/views/StaffDetail.vue'),
    meta: { title: '干部详情' }
  },
  {
    path: '/ai-result',
    name: 'AIResult',
    component: () => import('@/views/AIResult.vue'),
    meta: { title: 'AI搜索结果' }
  },
  {
    path: '/staff-filtered',
    name: 'StaffFiltered',
    component: () => import('@/views/StaffFiltered.vue'),
    meta: { title: '筛选结果' }
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - 纪检监察移动端`;
  }
  next();
});

export default router;
