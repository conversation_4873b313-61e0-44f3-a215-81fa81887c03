<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-08-14 18:01:21
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-14 20:54:28
 * @FilePath: /coos-dms-ui-app/src/views/Layout.vue
 * @Description:
-->
<template>
  <div class="layout">
    <router-view />
    <van-tabbar v-model="active" fixed placeholder>
      <van-tabbar-item to="/home">
        <template #icon>
          <van-icon name="wap-home" />
        </template>
        首页
      </van-tabbar-item>
      <van-tabbar-item to="/staff">
        <template #icon>
          <van-icon name="friends" />
        </template>
        干部
      </van-tabbar-item>
      <van-tabbar-item to="/training">
        <template #icon>
          <van-icon name="certificate" />
        </template>
        培训
      </van-tabbar-item>
      <van-tabbar-item to="/ai">
        <template #icon>
          <van-icon name="chat" />
        </template>
        AI
      </van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();
const active = ref(0);

// 根据路由设置active状态
const setActiveByRoute = (path) => {
  switch (path) {
    case '/home':
      active.value = 0;
      break;
    case '/staff':
      active.value = 1;
      break;
    case '/training':
      active.value = 2;
      break;
    case '/ai':
      active.value = 3;
      break;
    default:
      active.value = 0;
  }
};

// 监听路由变化
watch(
  () => route.path,
  (newPath) => {
    setActiveByRoute(newPath);
  },
  { immediate: true },
);
</script>

<style lang="scss" scoped>
.layout {
  min-height: 100vh;
  background-color: #f6f7fb;
}
</style>
