<template>
  <div class="ai-page">
    <van-nav-bar title="AI智能检索" fixed placeholder>
    </van-nav-bar>

    <div class="ai-content">
      <!-- AI Robot Avatar & Welcome Message -->
      <div class="welcome-section">
        <div class="robot-avatar">
          <div class="robot-body">
            <div class="robot-head">
              <div class="robot-eye left"></div>
              <div class="robot-eye right"></div>
            </div>
            <div class="robot-chest">
              <div class="robot-screen"></div>
            </div>
          </div>
        </div>
        <div class="welcome-message">
          <div class="message-bubble">
            Hi~我是智能助手，很高兴为您服务<br>
            我可以帮您查询回答各种工作问题～
          </div>
        </div>
      </div>

      <!-- Quick Questions Section -->
      <div class="quick-questions">
        <div class="section-header">
          <div class="title">猜你想问</div>
          <van-button 
            type="default" 
            size="mini" 
            plain
            @click="refreshQuestions"
          >
            <van-icon name="replay" />
            换一批
          </van-button>
        </div>
        
        <div class="questions-list">
          <div 
            v-for="question in quickQuestions" 
            :key="question.id"
            class="question-item"
            @click="selectQuestion(question.text)"
          >
            <van-icon name="question" class="question-icon" />
            <span class="question-text">{{ question.text }}</span>
            <van-icon name="arrow" class="arrow-icon" />
          </div>
        </div>
      </div>
    </div>

    <!-- Input Area -->
    <div class="input-area">
      <div class="input-container">
        <van-field
          v-model="inputMessage"
          placeholder="请输入消息"
          @keyup.enter="sendMessage"
        />
        <van-button 
          type="primary" 
          size="small" 
          round
          @click="sendMessage"
          :disabled="!inputMessage.trim()"
        >
          <van-icon name="arrow-up" />
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();
const inputMessage = ref('');

const quickQuestions = ref([
  {
    id: 1,
    text: '35岁以上有教育背景的纪检人员'
  },
  {
    id: 2,
    text: '正处级以上的党员干部'
  },
  {
    id: 3,
    text: '具有法律专业背景的在职人员'
  },
  {
    id: 4,
    text: '最近5年入职的硕士学历干部'
  }
]);

const selectQuestion = (questionText) => {
  inputMessage.value = questionText;
  sendMessage();
};

const sendMessage = () => {
  if (!inputMessage.value.trim()) return;
  
  // Navigate to results page with search query
  router.push({
    path: '/ai-result',
    query: {
      q: inputMessage.value
    }
  });
  
  inputMessage.value = '';
};

const refreshQuestions = () => {
  // In real app, this would fetch new questions from API
  console.log('Refresh questions');
};
</script>

<style lang="scss" scoped>
.ai-page {
  background: #f6f7fb;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  
  .ai-content {
    flex: 1;
    padding: 20px 16px 100px;
  }
  
  .welcome-section {
    display: flex;
    align-items: flex-start;
    margin-bottom: 32px;
    
    .robot-avatar {
      margin-right: 16px;
      flex-shrink: 0;
      
      .robot-body {
        width: 80px;
        height: 80px;
        position: relative;
        
        .robot-head {
          width: 60px;
          height: 45px;
          background: linear-gradient(135deg, #1989fa 0%, #4facfe 100%);
          border-radius: 30px 30px 15px 15px;
          position: relative;
          margin: 0 auto 5px;
          
          .robot-eye {
            width: 8px;
            height: 8px;
            background: #fff;
            border-radius: 50%;
            position: absolute;
            top: 15px;
            
            &.left {
              left: 15px;
            }
            
            &.right {
              right: 15px;
            }
          }
        }
        
        .robot-chest {
          width: 50px;
          height: 30px;
          background: linear-gradient(135deg, #1989fa 0%, #4facfe 100%);
          border-radius: 10px;
          margin: 0 auto;
          position: relative;
          
          .robot-screen {
            width: 20px;
            height: 12px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
          }
        }
      }
    }
    
    .welcome-message {
      flex: 1;
      margin-top: 10px;
      
      .message-bubble {
        background: #fff;
        border-radius: 16px 16px 16px 4px;
        padding: 16px;
        font-size: 14px;
        color: #323233;
        line-height: 1.5;
        box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        position: relative;
        
        &::before {
          content: '';
          position: absolute;
          left: -6px;
          bottom: 16px;
          width: 0;
          height: 0;
          border-top: 6px solid transparent;
          border-bottom: 6px solid transparent;
          border-right: 6px solid #fff;
        }
      }
    }
  }
  
  .quick-questions {
    background: #fff;
    border-radius: 16px;
    padding: 20px;
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      
      .title {
        font-size: 16px;
        font-weight: 600;
        color: #323233;
      }
    }
    
    .questions-list {
      .question-item {
        display: flex;
        align-items: center;
        padding: 16px;
        background: #f8f9ff;
        border-radius: 12px;
        margin-bottom: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        
        &:hover {
          background: #e6f2ff;
        }
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .question-icon {
          width: 20px;
          height: 20px;
          background: #1989fa;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          font-size: 10px;
          margin-right: 12px;
          flex-shrink: 0;
        }
        
        .question-text {
          flex: 1;
          font-size: 14px;
          color: #323233;
          line-height: 1.4;
        }
        
        .arrow-icon {
          color: #c8c9cc;
          font-size: 16px;
        }
      }
    }
  }
  
  .input-area {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    padding: 12px 16px;
    border-top: 1px solid #ebedf0;
    box-shadow: 0 -2px 8px rgba(0,0,0,0.08);
    
    .input-container {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .van-field {
        flex: 1;
        background: #f7f8fa;
        border-radius: 20px;
        
        :deep(.van-field__control) {
          padding: 10px 16px;
        }
      }
      
      .van-button {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        
        .van-icon {
          font-size: 16px;
        }
      }
    }
  }
}
</style>