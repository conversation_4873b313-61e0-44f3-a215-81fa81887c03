<template>
  <div class="ai-result-page">
    <van-nav-bar title="AI智能检索" fixed placeholder>
      <template #left>
        <van-icon name="arrow-left" @click="$router.back()" />
      </template>
    </van-nav-bar>

    <div class="result-content">
      <!-- User Query -->
      <div class="user-query">
        <div class="user-avatar">
          <van-icon name="user-o" />
        </div>
        <div class="query-bubble">
          {{ searchQuery }}
        </div>
      </div>

      <!-- AI Response -->
      <div class="ai-response">
        <div class="robot-avatar">
          <div class="robot-body">
            <div class="robot-head">
              <div class="robot-eye left"></div>
              <div class="robot-eye right"></div>
            </div>
            <div class="robot-chest">
              <div class="robot-screen"></div>
            </div>
          </div>
        </div>
        <div class="response-bubble">
          <div class="response-text">
            为您找到以下2条数据：
          </div>
          
          <!-- Results Table -->
          <div class="results-table">
            <div class="table-header">
              <div class="col">姓名</div>
              <div class="col">职务</div>
              <div class="col">专业</div>
            </div>
            <div class="table-row" v-for="result in searchResults" :key="result.id">
              <div class="col">{{ result.name }}</div>
              <div class="col">{{ result.position }}</div>
              <div class="col">{{ result.major }}</div>
            </div>
          </div>
          
          <!-- Apply to List Button -->
          <van-button 
            type="default" 
            size="small" 
            plain 
            class="apply-button"
            @click="applyToList"
          >
            <van-icon name="bars" />
            应用到列表
          </van-button>
        </div>
      </div>
    </div>

    <!-- Input Area -->
    <div class="input-area">
      <div class="input-container">
        <van-field
          v-model="inputMessage"
          placeholder="请输入消息"
          @keyup.enter="sendMessage"
        />
        <van-button 
          type="primary" 
          size="small" 
          round
          @click="sendMessage"
          :disabled="!inputMessage.trim()"
        >
          <van-icon name="arrow-up" />
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const route = useRoute();
const router = useRouter();
const inputMessage = ref('');
const searchQuery = ref('');

const searchResults = ref([
  {
    id: 1,
    name: '陈志强',
    position: '主任',
    major: '法学'
  },
  {
    id: 2,
    name: '王晓敏',
    position: '副主任',
    major: '学前教育'
  }
]);

onMounted(() => {
  searchQuery.value = route.query.q || '';
});

const sendMessage = () => {
  if (!inputMessage.value.trim()) return;
  
  // In real app, this would send the message and get a response
  console.log('Send message:', inputMessage.value);
  inputMessage.value = '';
};

const applyToList = () => {
  // Navigate to staff list with search results applied
  router.push({
    path: '/staff-filtered',
    query: {
      filter: searchQuery.value
    }
  });
};
</script>

<style lang="scss" scoped>
.ai-result-page {
  background: #f6f7fb;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  
  .result-content {
    flex: 1;
    padding: 20px 16px 100px;
  }
  
  .user-query {
    display: flex;
    align-items: flex-start;
    justify-content: flex-end;
    margin-bottom: 24px;
    
    .user-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 12px;
      flex-shrink: 0;
      
      .van-icon {
        color: #fff;
        font-size: 18px;
      }
    }
    
    .query-bubble {
      background: #1989fa;
      color: #fff;
      border-radius: 16px 4px 16px 16px;
      padding: 12px 16px;
      font-size: 14px;
      line-height: 1.4;
      max-width: 240px;
      word-wrap: break-word;
    }
  }
  
  .ai-response {
    display: flex;
    align-items: flex-start;
    margin-bottom: 24px;
    
    .robot-avatar {
      margin-right: 12px;
      flex-shrink: 0;
      
      .robot-body {
        width: 40px;
        height: 40px;
        position: relative;
        
        .robot-head {
          width: 30px;
          height: 22px;
          background: linear-gradient(135deg, #1989fa 0%, #4facfe 100%);
          border-radius: 15px 15px 8px 8px;
          position: relative;
          margin: 0 auto 3px;
          
          .robot-eye {
            width: 4px;
            height: 4px;
            background: #fff;
            border-radius: 50%;
            position: absolute;
            top: 8px;
            
            &.left {
              left: 8px;
            }
            
            &.right {
              right: 8px;
            }
          }
        }
        
        .robot-chest {
          width: 25px;
          height: 15px;
          background: linear-gradient(135deg, #1989fa 0%, #4facfe 100%);
          border-radius: 5px;
          margin: 0 auto;
          position: relative;
          
          .robot-screen {
            width: 10px;
            height: 6px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 1px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
          }
        }
      }
    }
    
    .response-bubble {
      background: #fff;
      border-radius: 4px 16px 16px 16px;
      padding: 16px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.08);
      position: relative;
      flex: 1;
      
      &::before {
        content: '';
        position: absolute;
        left: -6px;
        top: 16px;
        width: 0;
        height: 0;
        border-top: 6px solid transparent;
        border-bottom: 6px solid transparent;
        border-right: 6px solid #fff;
      }
      
      .response-text {
        font-size: 14px;
        color: #323233;
        margin-bottom: 16px;
        line-height: 1.4;
      }
      
      .results-table {
        border: 1px solid #ebedf0;
        border-radius: 8px;
        overflow: hidden;
        margin-bottom: 16px;
        
        .table-header {
          display: flex;
          background: #f7f8fa;
          
          .col {
            flex: 1;
            padding: 10px 12px;
            font-size: 12px;
            color: #646566;
            font-weight: 600;
            text-align: center;
            border-right: 1px solid #ebedf0;
            
            &:last-child {
              border-right: none;
            }
          }
        }
        
        .table-row {
          display: flex;
          background: #fff;
          border-top: 1px solid #ebedf0;
          
          .col {
            flex: 1;
            padding: 12px;
            font-size: 14px;
            color: #323233;
            text-align: center;
            border-right: 1px solid #ebedf0;
            
            &:last-child {
              border-right: none;
            }
          }
        }
      }
      
      .apply-button {
        border-color: #c8c9cc;
        color: #646566;
        
        .van-icon {
          margin-right: 4px;
        }
      }
    }
  }
  
  .input-area {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    padding: 12px 16px;
    border-top: 1px solid #ebedf0;
    box-shadow: 0 -2px 8px rgba(0,0,0,0.08);
    
    .input-container {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .van-field {
        flex: 1;
        background: #f7f8fa;
        border-radius: 20px;
        
        :deep(.van-field__control) {
          padding: 10px 16px;
        }
      }
      
      .van-button {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        
        .van-icon {
          font-size: 16px;
        }
      }
    }
  }
}
</style>