<template>
  <div class="staff-page">
    <van-nav-bar title="纪检队伍" fixed placeholder>
      <template #left>
        <van-icon name="arrow-left" @click="$router.back()" />
      </template>
      <template #right>
        <div class="ai-icon" @click="$router.push('/ai')">
          <span class="ai-text">AI</span>
        </div>
      </template>
    </van-nav-bar>

    <!-- Search Bar -->
    <div class="search-bar">
      <van-search 
        v-model="searchValue" 
        placeholder="请输入搜索关键词"
        @search="onSearch"
      />
    </div>

    <!-- Staff List -->
    <div class="staff-list">
      <div 
        v-for="staff in filteredStaffList" 
        :key="staff.id" 
        class="staff-item"
        @click="goToDetail(staff)"
      >
        <div class="avatar">
          <van-icon :name="staff.gender === '男' ? 'manager' : 'contact'" />
        </div>
        <div class="info">
          <div class="name">{{ staff.name }}</div>
          <div class="details">
            {{ staff.position }} | {{ staff.department }} | {{ staff.gender }} | {{ staff.age }}
          </div>
        </div>
        <div class="staff-id">
          ID:{{ staff.staffId }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();
const searchValue = ref('');

const staffList = ref([
  {
    id: 1,
    name: '陈志强',
    position: '主任',
    department: '纪检监察室',
    gender: '男',
    age: '48岁',
    staffId: 'CD27377401'
  },
  {
    id: 2,
    name: '王晓敏',
    position: '副主任',
    department: '纪检监察室',
    gender: '女',
    age: '43岁',
    staffId: 'CD27377401'
  },
  {
    id: 3,
    name: '张伟',
    position: '副主任',
    department: '纪检监察室',
    gender: '男',
    age: '48岁',
    staffId: 'CD27377401'
  },
  {
    id: 4,
    name: '蔡少伟',
    position: '副主任',
    department: '纪检监察室',
    gender: '男',
    age: '48岁',
    staffId: 'CD27377401'
  },
  {
    id: 5,
    name: '沈克诚',
    position: '副主任',
    department: '纪检监察室',
    gender: '男',
    age: '48岁',
    staffId: 'CD27377401'
  },
  {
    id: 6,
    name: '王小米',
    position: '副主任',
    department: '纪检监察室',
    gender: '男',
    age: '48岁',
    staffId: 'CD27377401'
  },
  {
    id: 7,
    name: '李桂兰',
    position: '副主任',
    department: '纪检监察室',
    gender: '男',
    age: '48岁',
    staffId: 'CD27377401'
  },
  {
    id: 8,
    name: '张秋枫',
    position: '副主任',
    department: '纪检监察室',
    gender: '男',
    age: '48岁',
    staffId: 'CD27377401'
  }
]);

const filteredStaffList = computed(() => {
  if (!searchValue.value) {
    return staffList.value;
  }
  return staffList.value.filter(staff => 
    staff.name.includes(searchValue.value) ||
    staff.position.includes(searchValue.value) ||
    staff.department.includes(searchValue.value)
  );
});

const onSearch = (value) => {
  console.log('Search:', value);
};

const goToDetail = (staff) => {
  router.push(`/staff/${staff.id}`);
};
</script>

<style lang="scss" scoped>
.staff-page {
  padding-bottom: 60px;
  background-color: #f6f7fb;
  min-height: 100vh;
  
  .ai-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #1989fa;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .ai-text {
      color: #fff;
      font-size: 12px;
      font-weight: 600;
    }
  }
  
  .search-bar {
    padding: 16px;
    background: #fff;
  }
  
  .staff-list {
    padding: 0 16px;
    
    .staff-item {
      background: #fff;
      border-radius: 12px;
      padding: 16px;
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      box-shadow: 0 2px 8px rgba(0,0,0,0.04);
      
      .avatar {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        
        .van-icon {
          color: #fff;
          font-size: 20px;
        }
      }
      
      .info {
        flex: 1;
        
        .name {
          font-size: 16px;
          font-weight: 600;
          color: #323233;
          margin-bottom: 4px;
        }
        
        .details {
          font-size: 12px;
          color: #969799;
        }
      }
      
      .staff-id {
        font-size: 12px;
        color: #969799;
      }
    }
  }
}
</style>