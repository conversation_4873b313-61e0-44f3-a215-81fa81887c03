<template>
  <div class="staff-page">
    <van-nav-bar title="纪检队伍" fixed placeholder>
      <template #right>
        <div class="ai-icon" @click="$router.push('/ai')">
          <span class="ai-text">AI</span>
        </div>
      </template>
    </van-nav-bar>

    <!-- Search Bar -->
    <van-search v-model="searchValue" placeholder="请输入搜索关键词" @search="onSearch" />

    <!-- Organization Structure -->
    <div class="organization-structure">
      <OrganizationNode
        v-for="org in filteredOrgStructure"
        :key="org.id"
        :organization="org"
        :level="0"
        @toggle="toggleOrg"
        @staff-click="goToDetail"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import OrganizationNode from '@/components/OrganizationNode.vue';

const router = useRouter();
const searchValue = ref('');

// 多层级组织架构数据结构
const orgStructure = ref([
  {
    id: 1,
    name: '纪检监察委员会',
    expanded: true,
    members: [
      {
        id: 1,
        name: '陈志强',
        position: '主任',
        department: '纪检监察委员会',
        gender: '男',
        age: '48岁',
      },
    ],
    children: [
      {
        id: 2,
        name: '纪检监察室',
        expanded: false,
        members: [
          {
            id: 2,
            name: '王晓敏',
            position: '主任',
            department: '纪检监察室',
            gender: '女',
            age: '43岁',
          },
          {
            id: 3,
            name: '张伟',
            position: '副主任',
            department: '纪检监察室',
            gender: '男',
            age: '48岁',
          },
        ],
        children: [
          {
            id: 3,
            name: '第一监察组',
            expanded: false,
            members: [
              {
                id: 4,
                name: '蔡少伟',
                position: '组长',
                department: '第一监察组',
                gender: '男',
                age: '45岁',
              },
              {
                id: 5,
                name: '沈克诚',
                position: '副组长',
                department: '第一监察组',
                gender: '男',
                age: '42岁',
              },
            ],
            children: [
              {
                id: 5,
                name: '案件审理小组',
                expanded: false,
                members: [
                  {
                    id: 9,
                    name: '赵小华',
                    position: '审理员',
                    department: '案件审理小组',
                    gender: '女',
                    age: '35岁',
                  },
                  {
                    id: 10,
                    name: '钱大明',
                    position: '助理审理员',
                    department: '案件审理小组',
                    gender: '男',
                    age: '29岁',
                  },
                ],
              },
            ],
          },
          {
            id: 4,
            name: '第二监察组',
            expanded: false,
            members: [
              {
                id: 6,
                name: '王小米',
                position: '组长',
                department: '第二监察组',
                gender: '女',
                age: '39岁',
              },
              {
                id: 7,
                name: '李桂兰',
                position: '副组长',
                department: '第二监察组',
                gender: '女',
                age: '41岁',
              },
            ],
          },
        ],
      },
      {
        id: 6,
        name: '综合科',
        expanded: false,
        members: [
          {
            id: 8,
            name: '张秋枫',
            position: '科长',
            department: '综合科',
            gender: '男',
            age: '44岁',
          },
        ],
      },
    ],
  },
]);

// 递归过滤函数
const filterOrgStructure = (orgs, searchTerm) => {
  return orgs
    .map((org) => {
      const filteredMembers = org.members
        ? org.members.filter(
            (staff) =>
              staff.name.includes(searchTerm) ||
              staff.position.includes(searchTerm) ||
              staff.department.includes(searchTerm),
          )
        : [];

      const filteredChildren = org.children ? filterOrgStructure(org.children, searchTerm) : [];

      return {
        ...org,
        members: filteredMembers,
        children: filteredChildren.filter(
          (child) => child.members.length > 0 || (child.children && child.children.length > 0),
        ),
      };
    })
    .filter((org) => org.members.length > 0 || (org.children && org.children.length > 0));
};

// 计算过滤后的组织架构
const filteredOrgStructure = computed(() => {
  if (!searchValue.value) {
    return orgStructure.value;
  }
  return filterOrgStructure(orgStructure.value, searchValue.value);
});

// 递归查找并切换组织状态
const findAndToggleOrg = (orgs, orgId) => {
  for (let org of orgs) {
    if (org.id === orgId) {
      org.expanded = !org.expanded;
      return true;
    }
    if (org.children && findAndToggleOrg(org.children, orgId)) {
      return true;
    }
  }
  return false;
};

// 切换组织架构展开/收起状态
const toggleOrg = (orgId) => {
  findAndToggleOrg(orgStructure.value, orgId);
};

const onSearch = (value) => {
  console.log('Search:', value);
};

const goToDetail = (staff) => {
  router.push(`/staff/${staff.id}`);
};
</script>

<style lang="scss" scoped>
.staff-page {
  background-color: #fff;
  min-height: calc(100vh - env(safe-area-inset-bottom) - var(--van-tabbar-height));

  .ai-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--van-primary-color);
    display: flex;
    align-items: center;
    justify-content: center;

    .ai-text {
      color: #fff;
      font-size: 12px;
      font-weight: 600;
    }
  }
  .van-search {
    padding: 12px 16px;
  }

  .organization-structure {
    padding: 0 16px;
  }
}
</style>
