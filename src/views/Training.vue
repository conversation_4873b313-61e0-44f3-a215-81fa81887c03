<template>
  <div class="training-page">
    <van-nav-bar title="培训管理" fixed placeholder>
      <template #right>
        <div class="ai-icon" @click="$router.push('/ai')">
          <span class="ai-text">AI</span>
        </div>
      </template>
    </van-nav-bar>

    <!-- Status Tabs -->
    <van-tabs v-model:active="activeTab" sticky>
      <van-tab title="未开始" name="pending">
        <div class="training-list">
          <div v-for="training in filteredTrainings.pending" :key="training.id" class="training-item">
            <div class="training-icon">
              <van-icon name="notes-o" />
            </div>
            <div class="training-info">
              <div class="title">{{ training.title }}</div>
              <div class="detail-row">
                <span class="label">主讲人：</span>
                <span class="value">{{ training.instructor }}</span>
              </div>
              <div class="detail-row">
                <span class="label">培训时间：</span>
                <span class="value">{{ training.time }}</span>
              </div>
              <div class="detail-row">
                <span class="label">培训地点：</span>
                <span class="value">{{ training.location }}</span>
              </div>
            </div>
          </div>
        </div>
      </van-tab>

      <van-tab title="已完成" name="completed">
        <div class="training-list">
          <div v-for="training in filteredTrainings.completed" :key="training.id" class="training-item">
            <div class="training-icon">
              <van-icon name="notes-o" />
            </div>
            <div class="training-info">
              <div class="title">{{ training.title }}</div>
              <div class="detail-row">
                <span class="label">主讲人：</span>
                <span class="value">{{ training.instructor }}</span>
              </div>
              <div class="detail-row">
                <span class="label">培训时间：</span>
                <span class="value">{{ training.time }}</span>
              </div>
              <div class="detail-row">
                <span class="label">培训地点：</span>
                <span class="value">{{ training.location }}</span>
              </div>
            </div>
          </div>
        </div>
      </van-tab>

      <van-tab title="已取消" name="cancelled">
        <div class="training-list">
          <div v-for="training in filteredTrainings.cancelled" :key="training.id" class="training-item">
            <div class="training-icon">
              <van-icon name="notes-o" />
            </div>
            <div class="training-info">
              <div class="title">{{ training.title }}</div>
              <div class="detail-row">
                <span class="label">主讲人：</span>
                <span class="value">{{ training.instructor }}</span>
              </div>
              <div class="detail-row">
                <span class="label">培训时间：</span>
                <span class="value">{{ training.time }}</span>
              </div>
              <div class="detail-row">
                <span class="label">培训地点：</span>
                <span class="value">{{ training.location }}</span>
              </div>
            </div>
          </div>
        </div>
      </van-tab>
    </van-tabs>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

const activeTab = ref('pending');

const trainingList = ref([
  {
    id: 1,
    title: '培训标题培训标题培训标题培训标题培训标题培训标...',
    instructor: '陈志强',
    time: '2025.03.18 10:30~2025.03.18 10:30',
    location: '13楼大会议室',
    status: 'pending',
  },
  {
    id: 2,
    title: '培训标题培训标题培训标题培训标题培训标题培训标...',
    instructor: '陈志强',
    time: '2025.03.18 10:30~2025.03.18 10:30',
    location: '13楼大会议室',
    status: 'pending',
  },
  {
    id: 3,
    title: '培训标题培训标题培训标题培训标题培训标题培训标...',
    instructor: '陈志强',
    time: '2025.03.18 10:30~2025.03.18 10:30',
    location: '13楼大会议室',
    status: 'pending',
  },
  {
    id: 4,
    title: '培训标题培训标题培训标题培训标题培训标题培训标...',
    instructor: '陈志强',
    time: '2025.03.18 10:30~2025.03.18 10:30',
    location: '13楼大会议室',
    status: 'pending',
  },
  {
    id: 5,
    title: '培训标题培训标题培训标题培训标题培训标题培训标...',
    instructor: '陈志强',
    time: '2025.03.18 10:30~2025.03.18 10:30',
    location: '13楼大会议室',
    status: 'pending',
  },
  {
    id: 6,
    title: '培训标题培训标题培训标题培训标题培训标题培训标...',
    instructor: '陈志强',
    time: '2025.03.18 10:30~2025.03.18 10:30',
    location: '13楼大会议室',
    status: 'completed',
  },
  {
    id: 7,
    title: '培训标题培训标题培训标题培训标题培训标题培训标...',
    instructor: '陈志强',
    time: '2025.03.18 10:30~2025.03.18 10:30',
    location: '13楼大会议室',
    status: 'cancelled',
  },
]);

const filteredTrainings = computed(() => {
  return {
    pending: trainingList.value.filter((t) => t.status === 'pending'),
    completed: trainingList.value.filter((t) => t.status === 'completed'),
    cancelled: trainingList.value.filter((t) => t.status === 'cancelled'),
  };
});
</script>

<style lang="scss" scoped>
.training-page {
  background-color: #f6f7fb;
  min-height: 100vh;

  .ai-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #1989fa;
    display: flex;
    align-items: center;
    justify-content: center;

    .ai-text {
      color: #fff;
      font-size: 12px;
      font-weight: 600;
    }
  }

  .training-list {
    padding: 16px;

    .training-item {
      background: #fff;
      border-radius: 12px;
      padding: 16px;
      margin-bottom: 12px;
      display: flex;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

      .training-icon {
        width: 32px;
        height: 32px;
        border-radius: 8px;
        background: #1989fa;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        flex-shrink: 0;

        .van-icon {
          color: #fff;
          font-size: 16px;
        }
      }

      .training-info {
        flex: 1;

        .title {
          font-size: 14px;
          font-weight: 600;
          color: #323233;
          margin-bottom: 8px;
          line-height: 1.4;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .detail-row {
          display: flex;
          margin-bottom: 4px;
          font-size: 12px;

          &:last-child {
            margin-bottom: 0;
          }

          .label {
            color: #646566;
            min-width: 60px;
            flex-shrink: 0;
          }

          .value {
            color: #323233;
            flex: 1;
          }
        }
      }
    }
  }
}
</style>
