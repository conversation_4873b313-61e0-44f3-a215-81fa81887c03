<template>
  <div class="staff-detail-page">
    <van-nav-bar title="干部人员详情" fixed placeholder>
      <template #left>
        <van-icon name="arrow-left" @click="$router.back()" />
      </template>
    </van-nav-bar>

    <div class="detail-content">
      <!-- Basic Information -->
      <div class="section">
        <div class="section-title">基本信息</div>
        <div class="info-list">
          <div class="info-item">
            <span class="label required">编号*</span>
            <span class="value">CD001</span>
          </div>
          <div class="info-item">
            <span class="label required">姓名*</span>
            <span class="value">陈志强</span>
          </div>
          <div class="info-item">
            <span class="label required">身份证号*</span>
            <span class="value">51184719B***********76</span>
          </div>
          <div class="info-item">
            <span class="label required">出生年月*</span>
            <span class="value">1989-02</span>
          </div>
          <div class="info-item">
            <span class="label">年龄</span>
            <span class="value">45</span>
          </div>
          <div class="info-item">
            <span class="label required">性别*</span>
            <span class="value">男</span>
          </div>
          <div class="info-item">
            <span class="label">民族</span>
            <span class="value">汉族</span>
          </div>
          <div class="info-item">
            <span class="label">政治面貌</span>
            <span class="value">党员</span>
          </div>
          <div class="info-item">
            <span class="label required">婚姻状况*</span>
            <span class="value">已婚</span>
          </div>
          <div class="info-item">
            <span class="label">学历</span>
            <span class="value">硕士研究生</span>
          </div>
          <div class="info-item">
            <span class="label required">最高学历对应专业*</span>
            <span class="value">法学</span>
          </div>
          <div class="info-item">
            <span class="label required">参加工作时间*</span>
            <span class="value">2000-06</span>
          </div>
          <div class="info-item">
            <span class="label required">手机号*</span>
            <span class="value">13557774890</span>
          </div>
          <div class="info-item">
            <span class="label">籍贯</span>
            <span class="value">四川成都</span>
          </div>
        </div>
      </div>

      <!-- Job Information -->
      <div class="section">
        <div class="section-title">入职信息</div>
        <div class="info-list">
          <div class="info-item">
            <span class="label">所属部门</span>
            <span class="value">市纪委监委办公室</span>
          </div>
          <div class="info-item">
            <span class="label">岗位</span>
            <span class="value">部长</span>
          </div>
          <div class="info-item">
            <span class="label">岗级</span>
            <span class="value">F1</span>
          </div>
          <div class="info-item">
            <span class="label">职级</span>
            <span class="value">高级</span>
          </div>
          <div class="info-item">
            <span class="label">员工类别</span>
            <span class="value">编制内正式员工</span>
          </div>
          <div class="info-item">
            <span class="label">入职日期</span>
            <span class="value">2008-02-18</span>
          </div>
          <div class="info-item">
            <span class="label">劳动合同主体</span>
            <span class="value">集团</span>
          </div>
          <div class="info-item">
            <span class="label">试用期</span>
            <span class="value">无</span>
          </div>
          <div class="info-item">
            <span class="label">合同类型</span>
            <span class="value">正式合同</span>
          </div>
          <div class="info-item">
            <span class="label">职称及其他证书</span>
            <span class="value">高级职称</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();

onMounted(() => {
  console.log('Staff ID:', route.params.id);
});
</script>

<style lang="scss" scoped>
.staff-detail-page {
  background-color: #f6f7fb;
  min-height: 100vh;
  padding-bottom: 60px;
  
  .detail-content {
    padding: 16px;
  }
  
  .section {
    background: #fff;
    border-radius: 12px;
    margin-bottom: 16px;
    overflow: hidden;
    
    .section-title {
      padding: 16px;
      font-size: 16px;
      font-weight: 600;
      color: #323233;
      border-bottom: 1px solid #ebedf0;
      position: relative;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 16px;
        background: #1989fa;
      }
    }
    
    .info-list {
      .info-item {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        border-bottom: 1px solid #f7f8fa;
        
        &:last-child {
          border-bottom: none;
        }
        
        .label {
          min-width: 120px;
          font-size: 14px;
          color: #646566;
          position: relative;
          
          &.required::after {
            content: '*';
            color: #ee0a24;
            margin-left: 2px;
          }
        }
        
        .value {
          flex: 1;
          font-size: 14px;
          color: #323233;
          text-align: right;
        }
      }
    }
  }
}
</style>