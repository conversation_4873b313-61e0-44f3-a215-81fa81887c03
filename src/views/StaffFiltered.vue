<template>
  <div class="staff-filtered-page">
    <van-nav-bar title="纪检队伍" fixed placeholder>
      <template #left>
        <van-icon name="arrow-left" @click="$router.back()" />
      </template>
      <template #right>
        <div class="ai-icon" @click="$router.push('/ai')">
          <span class="ai-text">AI</span>
        </div>
      </template>
    </van-nav-bar>

    <!-- Search Bar -->
    <div class="search-bar">
      <van-search 
        v-model="searchValue" 
        placeholder="请输入搜索关键词"
        @search="onSearch"
      />
    </div>

    <!-- Filter Notice -->
    <div class="filter-notice" v-if="filterText">
      <div class="notice-content">
        <van-icon name="filter-o" />
        <span class="notice-text">已筛选"{{ filterText }}"的干部：</span>
      </div>
    </div>

    <!-- Staff List -->
    <div class="staff-list">
      <div 
        v-for="staff in filteredStaffList" 
        :key="staff.id" 
        class="staff-item"
        @click="goToDetail(staff)"
      >
        <div class="avatar">
          <van-icon :name="staff.gender === '男' ? 'manager' : 'contact'" />
        </div>
        <div class="info">
          <div class="name">{{ staff.name }}</div>
          <div class="details">
            {{ staff.position }} | {{ staff.department }} | {{ staff.gender }} | {{ staff.age }}
          </div>
        </div>
        <div class="staff-id">
          ID:{{ staff.staffId }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';

const router = useRouter();
const route = useRoute();
const searchValue = ref('');
const filterText = ref('');

const staffList = ref([
  {
    id: 1,
    name: '陈志强',
    position: '主任',
    department: '纪检监察室',
    gender: '男',
    age: '48岁',
    staffId: 'CD27377401'
  },
  {
    id: 2,
    name: '王晓敏',
    position: '副主任',
    department: '纪检监察室',
    gender: '女',
    age: '43岁',
    staffId: 'CD27377401'
  }
]);

const filteredStaffList = computed(() => {
  if (!searchValue.value) {
    return staffList.value;
  }
  return staffList.value.filter(staff => 
    staff.name.includes(searchValue.value) ||
    staff.position.includes(searchValue.value) ||
    staff.department.includes(searchValue.value)
  );
});

onMounted(() => {
  filterText.value = route.query.filter || '';
});

const onSearch = (value) => {
  console.log('Search:', value);
};

const goToDetail = (staff) => {
  router.push(`/staff/${staff.id}`);
};
</script>

<style lang="scss" scoped>
.staff-filtered-page {
  padding-bottom: 60px;
  background-color: #f6f7fb;
  min-height: 100vh;
  
  .ai-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #1989fa;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .ai-text {
      color: #fff;
      font-size: 12px;
      font-weight: 600;
    }
  }
  
  .search-bar {
    padding: 16px;
    background: #fff;
  }
  
  .filter-notice {
    padding: 12px 16px;
    background: #fff3e0;
    margin-bottom: 16px;
    
    .notice-content {
      display: flex;
      align-items: center;
      
      .van-icon {
        color: #ff8f00;
        font-size: 16px;
        margin-right: 8px;
      }
      
      .notice-text {
        color: #e65100;
        font-size: 14px;
      }
    }
  }
  
  .staff-list {
    padding: 0 16px;
    
    .staff-item {
      background: #fff;
      border-radius: 12px;
      padding: 16px;
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      box-shadow: 0 2px 8px rgba(0,0,0,0.04);
      
      .avatar {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        
        .van-icon {
          color: #fff;
          font-size: 20px;
        }
      }
      
      .info {
        flex: 1;
        
        .name {
          font-size: 16px;
          font-weight: 600;
          color: #323233;
          margin-bottom: 4px;
        }
        
        .details {
          font-size: 12px;
          color: #969799;
        }
      }
      
      .staff-id {
        font-size: 12px;
        color: #969799;
      }
    }
  }
}
</style>