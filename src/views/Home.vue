<template>
  <div class="home-page">
    <!-- Header -->
    <div class="header">
      <div class="user-info">
        <van-icon name="manager" class="avatar" />
        <div class="info">
          <div class="title">超级管理员</div>
          <div class="subtitle">部长 | 纪检监察室</div>
        </div>
      </div>
      <van-icon name="search" class="search-icon" @click="$router.push('/ai')" />
    </div>

    <!-- Quick Access Cards -->
    <div class="quick-access">
      <div class="left">
        <div class="access-card blue" @click="$router.push('/ai')">
          <div class="card-content">
            <div class="title">智能检索</div>
            <div class="subtitle">AI智能查询</div>
            <van-icon name="chat" class="card-icon" />
          </div>
        </div>
      </div>
      <div class="right">
        <div class="access-card green" @click="$router.push('/staff')">
          <div class="card-content">
            <div class="title">纪检队伍</div>
            <div class="subtitle">队伍管理一体化</div>
            <van-icon name="friends" class="card-icon" />
          </div>
        </div>
        <div class="access-card orange" @click="$router.push('/training')">
          <div class="card-content">
            <div class="title">培训管理</div>
            <div class="subtitle">培训数据管理</div>
            <van-icon name="certificate" class="card-icon" />
          </div>
        </div>
      </div>
    </div>

    <!-- Statistics Overview -->

    <section-block class="stats-section" title="纪检队伍总览">
      <div class="stats-grid">
        <div v-for="(config, key) in STATISTIC_CONFIG" :key="key" class="stat-item">
          <div class="label">{{ config.title }}</div>
          <div class="value">{{ config.value }}</div>
          <div class="sub-stats" v-if="config.fullTime !== undefined">
            <span class="sub-title sub-a">专职</span>
            <label class="sub-value">{{ config.fullTime }}</label>
            <label class="sub-separator">/</label>
            <span class="sub-title sub-b">兼职</span>
            <label class="sub-value">{{ config.partTime }}</label>
          </div>
          <div class="sub-stats" v-else-if="config.companyCount !== undefined">
            <span class="sub-title sub-c">缺编公司</span>
            <label class="sub-value">{{ config.companyCount }}</label>
            <label class="sub-separator">/</label>
            <span class="sub-title sub-d">缺编部门</span>
            <label class="sub-value">{{ config.departmentCount }}</label>
          </div>
        </div>
      </div>
    </section-block>

    <!-- Age Distribution Chart -->
    <section-block class="chart-section" title="干部年龄分布" :support-refresh="true" @refresh="handleRefresh">
      <div class="chart-container">
        <v-chart :option="ageChartOption" style="height: 200px" />
      </div>
    </section-block>

    <!-- Education Distribution Chart -->
    <section-block class="chart-section" title="学历结构分布" :support-refresh="true" @refresh="handleRefresh">
      <div class="chart-container">
        <v-chart :option="educationChartOption" style="height: 200px" />
      </div>
    </section-block>

    <!-- Position Distribution Chart -->
    <section-block class="chart-section" title="职务级别分布" :support-refresh="true" @refresh="handleRefresh">
      <div class="chart-container">
        <v-chart :option="positionChartOption" style="height: 200px" />
      </div>
    </section-block>

    <!-- Professional Quality Chart -->
    <section-block class="chart-section" title="干部专业资质情况" :support-refresh="true" @refresh="handleRefresh">
      <div class="chart-container">
        <v-chart :option="qualificationChartOption" style="height: 200px" />
      </div>
    </section-block>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import SectionBlock from '@/components/section.vue';

const STATISTIC_CONFIG = ref({
  zgbs: { title: '总干部数', value: 48, fullTime: 35, partTime: 13 },
  zggbs: { title: '在岗干部数', value: 45, fullTime: 32, partTime: 13 },
  jdgbs: { title: '借调干部数', value: 8, fullTime: 5, partTime: 3 },
  qbgbs: { title: '缺编干部数', value: 12, companyCount: 5, departmentCount: 7 },
  gggbs: { title: '任高管干部数', value: 15, fullTime: 10, partTime: 5 },
  zyzzgbs: { title: '具备专业资质干部数', value: 30, fullTime: 22, partTime: 8 },
  jdjjdqs: { title: '借调即将到期干部数', value: 5, fullTime: 3, partTime: 2 },
  jtlzgbs: { title: '进退留转干部数', value: 3, fullTime: 2, partTime: 1 },
});
// 刷新处理函数
const handleRefresh = () => {
  console.log('刷新图表数据');
  // 这里可以添加刷新数据的逻辑
};

// 更新统计数据的函数
const updateStatistics = (newData) => {
  Object.keys(newData).forEach((key) => {
    if (STATISTIC_CONFIG.value[key]) {
      STATISTIC_CONFIG.value[key] = { ...STATISTIC_CONFIG.value[key], ...newData[key] };
    }
  });
};

// 获取统计数据的函数（模拟API调用）
const fetchStatistics = async () => {
  try {
    // 这里可以调用实际的API
    // const response = await statisticsApi.getOverview();
    // updateStatistics(response.data);
    console.log('获取统计数据');
  } catch (error) {
    console.error('获取统计数据失败:', error);
  }
};

// 组件挂载时初始化数据
onMounted(() => {
  fetchStatistics();
});

// Age distribution chart
const ageChartOption = ref({
  xAxis: {
    type: 'category',
    data: ['25-30岁', '31-35岁', '36-40岁', '41-45岁', '46-50岁'],
    axisLabel: {
      fontSize: 10,
    },
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      fontSize: 10,
    },
  },
  grid: {
    left: 40,
    right: 20,
    top: 20,
    bottom: 40,
  },
  series: [
    {
      data: [35, 75, 50, 65, 20],
      type: 'bar',
      itemStyle: {
        color: '#07c160',
      },
      barWidth: 30,
    },
  ],
});

// Education distribution chart
const educationChartOption = ref({
  tooltip: {
    trigger: 'item',
    formatter: '{b}: {c} ({d}%)',
  },
  legend: {
    orient: 'vertical',
    right: 10,
    top: 'center',
    textStyle: {
      fontSize: 10,
    },
  },
  series: [
    {
      type: 'pie',
      radius: ['30%', '60%'],
      center: ['40%', '50%'],
      data: [
        { value: 152, name: '本科', itemStyle: { color: '#1989fa' } },
        { value: 45, name: '硕士', itemStyle: { color: '#52c41a' } },
        { value: 30, name: '博士', itemStyle: { color: '#fa8c16' } },
        { value: 20, name: '大专', itemStyle: { color: '#eb2f96' } },
      ],
      label: {
        show: false,
      },
    },
  ],
});

// Position distribution chart
const positionChartOption = ref({
  grid: {
    left: 60,
    right: 30,
    top: 20,
    bottom: 40,
  },
  xAxis: {
    type: 'value',
    axisLabel: {
      fontSize: 10,
    },
  },
  yAxis: {
    type: 'category',
    data: ['科员', '副部长', '部长', '副总经理', '总经理', '副总裁', '总裁'],
    axisLabel: {
      fontSize: 10,
    },
  },
  series: [
    {
      data: [180, 150, 120, 90, 60, 30, 15],
      type: 'bar',
      itemStyle: {
        color: '#1989fa',
      },
      barWidth: 20,
    },
  ],
});

// Professional qualification chart
const qualificationChartOption = ref({
  tooltip: {
    trigger: 'item',
    formatter: '{b}: {c} ({d}%)',
  },
  legend: {
    orient: 'vertical',
    right: 10,
    top: 'center',
    textStyle: {
      fontSize: 10,
    },
  },
  series: [
    {
      type: 'pie',
      radius: ['30%', '60%'],
      center: ['40%', '50%'],
      data: [
        { value: 180, name: '高级职称', itemStyle: { color: '#1989fa' } },
        { value: 48, name: '中级职称', itemStyle: { color: '#52c41a' } },
        { value: 35, name: '初级职称', itemStyle: { color: '#fa8c16' } },
        { value: 25, name: '无职称', itemStyle: { color: '#eb2f96' } },
      ],
      label: {
        show: false,
      },
    },
  ],
});
</script>

<style lang="scss" scoped>
.home-page {
  background: #eef2fa;
  padding-bottom: 16px;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: #fff;
    margin-bottom: 16px;

    .user-info {
      display: flex;
      align-items: center;

      .avatar {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: 24px;
        margin-right: 12px;
      }

      .info {
        .title {
          font-size: 18px;
          font-weight: 600;
          color: #323233;
          margin-bottom: 4px;
        }

        .subtitle {
          font-size: 12px;
          color: #969799;
        }
      }
    }

    .search-icon {
      font-size: 24px;
      color: #969799;
    }
  }

  .quick-access {
    background: #ffffff;
    box-shadow: 0px 2px 24px 0px rgba(11, 51, 84, 0.05);
    border-radius: 16px 16px 16px 16px;
    padding: 16px 12px;
    margin: 16px 15px;
    display: flex;
    gap: 10px;
    .left,
    .right {
      width: 50%;
    }
    .left {
      display: flex;
      flex-direction: column;
    }
    .right {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
    .access-card {
      border-radius: 12px;
      padding: 16px;
      position: relative;
      overflow: hidden;
      flex: 1;

      &.blue {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        height: 100%;
        display: flex;
        align-items: center;
      }

      &.green {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      }

      &.orange {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }

      .card-content {
        color: #fff;

        .title {
          font-size: 16px;
          font-weight: 600;
          margin-bottom: 4px;
        }

        .subtitle {
          font-size: 12px;
          opacity: 0.9;
        }

        .card-icon {
          position: absolute;
          right: 16px;
          top: 50%;
          transform: translateY(-50%);
          font-size: 32px;
          opacity: 0.3;
        }
      }
    }
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;

    .stat-item {
      padding: 8px 0 8px 12px;
      background: linear-gradient(331deg, #f9faff 0%, #eef4fe 100%);
      border-radius: 12px 12px 12px 12px;
      .label {
        height: 20px;
        font-size: 14px;
        color: #6e7c98;
      }

      .value {
        height: 30px;
        font-family: TCloudNumber, TCloudNumber;
        font-size: 28px;
        color: #15224c;
        line-height: 30px;
      }

      .sub-stats {
        display: flex;
        align-items: center;
        margin-top: 3px;
        .sub-title {
          height: 16px;
          border-radius: 3px;
          font-size: 10px;
          padding: 1px 2px;
          margin-right: 4px;
          display: inline-block;
          &.sub-a {
            background: rgba(69, 126, 254, 0.1);
            color: #457efe;
          }
          &.sub-b {
            background: rgba(81, 205, 160, 0.1);
            color: #21bd85;
          }
          &.sub-c {
            background: rgba(255, 157, 115, 0.1);
            color: #ff9d73;
          }
          &.sub-d {
            background: rgba(85, 170, 255, 0.1);
            color: #55aaff;
          }
        }
        .sub-value {
          height: 22px;
          font-family: TCloudNumber, TCloudNumber;
          font-size: 16px;
          color: #2f446b;
        }
        .sub-separator {
          width: 10px;
          text-align: center;
          display: inline-block;
          font-size: 20px;
          color: #b0bbce;
          // line-height: 20px;
          margin: 0 4px;
          position: relative;
          top: -2px;
        }
      }
    }
  }

  .chart-container {
    width: 100%;
  }
}
</style>
