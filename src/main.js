/**
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-08-14 17:10:11
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-14 17:34:01
 * @FilePath: /coos-dms-ui-app/src/main.js
 * @Description:
 */
import { createApp } from 'vue';
import { createPinia } from 'pinia';
import router from './router';
import App from './App.vue';

// Vant UI
import Vant from 'vant';
import 'vant/lib/index.css';

// Touch emulator for desktop
import '@vant/touch-emulator';

const app = createApp(App);

app.use(createPinia());
app.use(router);
app.use(Vant);

app.mount('#app');
