/**
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-08-14 17:10:11
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-14 17:34:01
 * @FilePath: /coos-dms-ui-app/src/main.js
 * @Description:
 */
import { createApp } from 'vue';
import { createPinia } from 'pinia';
import router from './router';
import App from './App.vue';

// Vant UI 样式（按需引入时仍需要基础样式）
import 'vant/lib/index.css';

// Touch emulator for desktop
import '@vant/touch-emulator';

// ECharts
import * as echarts from 'echarts';
import VChart from 'vue-echarts';

// 注册必要的图表组件
import { 
  CanvasRenderer 
} from 'echarts/renderers';
import {
  BarChart,
  PieChart
} from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components';

// 注册ECharts组件
echarts.use([
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Pie<PERSON><PERSON>,
  TitleComponent,
  Tooltip<PERSON>omponent,
  LegendComponent,
  GridComponent
]);

const app = createApp(App);

app.use(createPinia());
app.use(router);

// 全局注册VChart组件
app.component('VChart', VChart);

app.mount('#app');
