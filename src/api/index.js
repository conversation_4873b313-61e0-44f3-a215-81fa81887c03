import request from './request'

// 统计相关API
export const statisticsApi = {
  // 获取统计概览
  getOverview: () => request.get('/statistics/overview'),
  
  // 获取案件统计
  getCaseStats: (params) => request.get('/statistics/cases', { params }),
  
  // 获取人员统计
  getPersonnelStats: () => request.get('/statistics/personnel'),
  
  // 获取培训统计
  getTrainingStats: () => request.get('/statistics/training')
}

// 纪检队伍相关API
export const teamApi = {
  // 获取队伍列表
  getTeamList: (params) => request.get('/team/list', { params }),
  
  // 获取队伍详情
  getTeamDetail: (id) => request.get(`/team/detail/${id}`),
  
  // 获取人员列表
  getPersonnelList: (params) => request.get('/team/personnel', { params }),
  
  // 获取组织架构
  getOrganization: () => request.get('/team/organization')
}

// AI检索相关API
export const aiSearchApi = {
  // 智能搜索
  search: (params) => request.post('/ai/search', params),
  
  // 获取搜索历史
  getSearchHistory: () => request.get('/ai/search/history'),
  
  // 清除搜索历史
  clearSearchHistory: () => request.delete('/ai/search/history'),
  
  // 获取热门搜索
  getHotSearch: () => request.get('/ai/search/hot')
}

// 纪检培训相关API
export const trainingApi = {
  // 获取课程列表
  getCourseList: (params) => request.get('/training/courses', { params }),
  
  // 获取课程详情
  getCourseDetail: (id) => request.get(`/training/courses/${id}`),
  
  // 获取我的学习
  getMyLearning: () => request.get('/training/my-learning'),
  
  // 开始学习
  startLearning: (courseId) => request.post(`/training/courses/${courseId}/start`),
  
  // 完成学习
  completeLearning: (courseId, progress) => 
    request.post(`/training/courses/${courseId}/complete`, progress),
  
  // 获取考试列表
  getExamList: (params) => request.get('/training/exams', { params }),
  
  // 获取考试详情
  getExamDetail: (id) => request.get(`/training/exams/${id}`),
  
  // 提交考试
  submitExam: (examId, answers) => 
    request.post(`/training/exams/${examId}/submit`, { answers }),
  
  // 获取考试结果
  getExamResult: (examId) => request.get(`/training/exams/${examId}/result`)
}
