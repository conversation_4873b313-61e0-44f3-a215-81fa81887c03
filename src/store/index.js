import { defineStore } from 'pinia'

// 用户信息store
export const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: {
      id: '',
      name: '',
      avatar: '',
      department: '',
      position: ''
    },
    isLogin: false
  }),
  
  actions: {
    setUserInfo(userInfo) {
      this.userInfo = userInfo
      this.isLogin = true
    },
    
    logout() {
      this.userInfo = {
        id: '',
        name: '',
        avatar: '',
        department: '',
        position: ''
      }
      this.isLogin = false
    }
  }
})

// 应用配置store
export const useAppStore = defineStore('app', {
  state: () => ({
    loading: false,
    theme: 'light'
  }),
  
  actions: {
    setLoading(loading) {
      this.loading = loading
    },
    
    setTheme(theme) {
      this.theme = theme
    }
  }
})
