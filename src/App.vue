<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-08-14 16:55:12
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-14 17:34:39
 * @FilePath: /coos-dms-ui-app/src/App.vue
 * @Description:
-->
<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script setup>
import { onMounted } from 'vue';

onMounted(() => {
  // 设置页面标题
  document.title = '纪检监察移动端';
  
  // 设置viewport meta标签以确保移动端适配
  const viewport = document.querySelector('meta[name=viewport]');
  if (viewport) {
    viewport.setAttribute('content', 'width=device-width,initial-scale=1.0,user-scalable=no,viewport-fit=cover');
  }
});
</script>

<style lang="scss">
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  width: 100%;
  height: 100vh;
  background-color: #f6f7fb;
}

// 全局样式重置
.van-nav-bar {
  background: #fff !important;
  
  .van-nav-bar__title {
    color: #323233 !important;
    font-weight: 600 !important;
  }
}

.van-tabbar {
  background: #fff !important;
  border-top: 1px solid #ebedf0 !important;
}

.van-tabbar-item {
  color: #969799 !important;
  
  &.van-tabbar-item--active {
    color: #1989fa !important;
  }
}
</style>
