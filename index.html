<!--
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-08-14 18:04:57
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-14 18:41:51
 * @FilePath: /coos-dms-ui-app/index.html
 * @Description:
-->
<!DOCTYPE html>
<html lang="zh-CN">

  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <title>纪检监察移动端</title>
    <script>
      // 移动端rem适配方案
      (function flexible(window, document) {
        var docEl = document.documentElement;
        var dpr = window.devicePixelRatio || 1;

        function setRemUnit() {
          // 750px设计稿，375px屏幕宽度下，1rem = 37.5px
          var rem = docEl.clientWidth / 10;
          docEl.style.fontSize = rem + 'px';
        }

        setRemUnit();

        // 屏幕尺寸变化时重新计算
        window.addEventListener('resize', setRemUnit);
        window.addEventListener('orientationchange', setRemUnit);
        window.addEventListener('pageshow', function (e) {
          if (e.persisted) {
            setRemUnit();
          }
        });

      })(window, document);
    </script>
  </head>

  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>
  </body>

</html>
