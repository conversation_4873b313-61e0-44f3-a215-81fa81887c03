{"types": {"feat": {"value": "feat", "emoji": "✨", "description": "新功能"}, "fix": {"value": "fix", "emoji": "🐛", "description": "修复bug"}, "docs": {"value": "docs", "emoji": "📝", "description": "文档更新"}, "style": {"value": "style", "emoji": "💄", "description": "代码格式调整"}, "refactor": {"value": "refactor", "emoji": "♻️", "description": "代码重构"}, "perf": {"value": "perf", "emoji": "⚡️", "description": "性能优化"}, "test": {"value": "test", "emoji": "✅", "description": "测试相关"}, "chore": {"value": "chore", "emoji": "🔧", "description": "构建过程或辅助工具的变动"}, "ci": {"value": "ci", "emoji": "🚀", "description": "CI/CD 相关"}, "build": {"value": "build", "emoji": "📦", "description": "构建相关"}, "revert": {"value": "revert", "emoji": "⏪️", "description": "回滚"}, "wip": {"value": "wip", "emoji": "🚧", "description": "开发中"}, "security": {"value": "security", "emoji": "🔒", "description": "安全相关"}, "deps": {"value": "deps", "emoji": "📦", "description": "依赖更新"}}, "messages": {"type": "🎯 选择更改类型:", "scope": "📂 更改范围 (可选):", "subject": "💬 简短描述:", "body": "📄 详细描述 (可选):", "breaking": "💥 破坏性变更 (可选):", "footer": "🔗 关联问题 (可选):", "confirmCommit": "✅ 确认提交?"}, "skipQuestions": ["body", "breaking", "footer"], "subjectLimit": 100, "subjectLineBreak": "wrap", "allowCustomScopes": true, "allowBreakingChanges": ["feat", "fix", "refactor", "perf"], "appendBranchNameToCommitMessage": false, "breakingPrefix": "💥 ", "footerPrefix": "🔗 "}