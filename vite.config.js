/**
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-08-14 16:50:00
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-14 20:48:17
 * @FilePath: /coos-dms-ui-app/vite.config.js
 * @Description:
 */
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';
import postcssPxToRem from 'postcss-pxtorem';
import Components from 'unplugin-vue-components/vite';
import { VantResolver } from '@vant/auto-import-resolver';

export default defineConfig({
  plugins: [
    vue(),
    Components({
      resolvers: [VantResolver()],
    }),
  ],
  resolve: {
    alias: {
      '@': resolve(process.cwd(), 'src'),
    },
  },
  server: {
    host: '0.0.0.0',
    port: 3000,
    open: true,
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    minify: 'terser',
    rollupOptions: {
      output: {
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: '[ext]/[name]-[hash].[ext]',
      },
    },
  },
  css: {
    preprocessorOptions: {
      scss: {},
    },
    postcss: {
      plugins: [
        postcssPxToRem({
          rootValue: 37.5, // 基于750px设计稿，375px屏幕宽度，1rem = 37.5px
          unitPrecision: 5, // 保留小数位数
          propList: ['*'], // 所有属性都转换
          selectorBlackList: ['.van-', '[class*="van-"]'], // 排除vant的class和包含van-的class
          replace: true, // 直接替换，不保留px
          mediaQuery: false, // 不允许在媒体查询中转换px
          minPixelValue: 2, // 小于2px的不转换
          exclude: /node_modules\/vant/, // 排除vant目录
        }),
      ],
    },
  },
});
